@import "tailwindcss";

/* 确保基本样式正常工作 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
}

/* 暗色主题基础样式 */
.dark {
  background-color: #000000;
  color: #f5f5f7;
}

:root {
  /* 固定使用暗黑模式的颜色 */
  --background: #000000;
  --foreground: #f5f5f7;
  --primary: #009bc7;
  --primary-light: #33afcf;
  --primary-dark: #007ba0;

  /* 添加更多暗黑主题相关的颜色变量 */
  --dropdown-bg: #1a1a1a;
  --dropdown-border: #2d2d30;
  --dropdown-hover: #2d2d30;
  --dropdown-text: #f5f5f7;
  --dropdown-text-disabled: #8e8e93;
}

/* Apple-style smooth scrolling */
html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, "San Francisco",
    "Helvetica Neue", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: -0.015em;
  margin: 0;
  min-height: 100vh;
}

/* Apple-style container */
.container-apple {
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

@media (min-width: 640px) {
  .container-apple {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .container-apple {
    padding-left: 2.5rem;
    padding-right: 2.5rem;
  }
}

/* Apple-style navbar */
.navbar-apple {
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
  transition: all 0.3s ease;
  width: 100%;
  overflow-x: hidden;
}

/* Mobile menu styles */
@media (max-width: 767px) {
  .navbar-apple .container-apple {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .mobile-menu-container {
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .dark .mobile-menu-container {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  }
}

/* Apple-style card */
.card-apple {
  background-color: rgba(30, 30, 32, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.25, 0.1, 0.25, 1);
  max-width: 100%;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

@media (min-width: 640px) {
  .card-apple {
    border-radius: 20px;
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.15);
  }
}

.card-apple:hover {
  transform: translateY(-6px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

@media (min-width: 640px) {
  .card-apple:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  }
}

/* Apple-style buttons */
.btn-apple {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: 980px;
  font-weight: 500;
  font-size: 1rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.btn-apple-primary {
  background-color: var(--primary);
  color: white;
}

.btn-apple-primary:hover {
  background-color: var(--primary-dark);
  transform: scale(1.02);
}

.btn-apple-secondary {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--foreground);
}

.btn-apple-secondary:hover {
  background-color: rgba(255, 255, 255, 0.15);
  transform: scale(1.02);
}

/* Apple-style form elements */
.input-apple {
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  width: 100%;
  color: var(--foreground);
}

.input-apple:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 4px rgba(0, 155, 199, 0.1);
}

.input-apple::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

/* Animation utilities */
.fade-in {
  opacity: 0;
  animation: fadeIn 0.8s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Text utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Admin styles */
.admin-sidebar-fixed {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 50;
}

/* Admin Dashboard Cards */
.admin-card {
  background: linear-gradient(
    135deg,
    rgba(30, 30, 32, 0.9) 0%,
    rgba(20, 20, 22, 0.9) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.admin-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  border-color: rgba(0, 155, 199, 0.2);
}

/* Admin Stats Cards */
.admin-stat-card {
  background: linear-gradient(
    135deg,
    rgba(0, 155, 199, 0.1) 0%,
    rgba(0, 123, 160, 0.05) 100%
  );
  border: 1px solid rgba(0, 155, 199, 0.2);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

.admin-stat-card:hover {
  background: linear-gradient(
    135deg,
    rgba(0, 155, 199, 0.15) 0%,
    rgba(0, 123, 160, 0.08) 100%
  );
  border-color: rgba(0, 155, 199, 0.3);
  transform: translateY(-1px);
}

/* Admin Table Styles */
.admin-table {
  background: rgba(30, 30, 32, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  overflow: hidden;
}

.admin-table-header {
  background: rgba(0, 155, 199, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-table-row {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  transition: all 0.2s ease;
}

.admin-table-row:hover {
  background: rgba(255, 255, 255, 0.02);
}

/* Admin Sidebar Enhanced */
.admin-sidebar {
  background: linear-gradient(
    180deg,
    rgba(15, 15, 17, 0.95) 0%,
    rgba(10, 10, 12, 0.98) 100%
  );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.08);
}

.admin-nav-item {
  position: relative;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 0 12px;
}

.admin-nav-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.admin-nav-item.active {
  background: linear-gradient(
    135deg,
    rgba(0, 155, 199, 0.2) 0%,
    rgba(0, 123, 160, 0.1) 100%
  );
  border: 1px solid rgba(0, 155, 199, 0.3);
}

.admin-nav-item.active::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--primary);
  border-radius: 0 2px 2px 0;
}

/* Admin Form Elements */
.admin-input {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  color: var(--foreground);
  transition: all 0.3s ease;
}

.admin-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(0, 155, 199, 0.1);
  background: rgba(255, 255, 255, 0.08);
}

.admin-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

/* Admin Buttons */
.admin-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-btn-primary {
  background: linear-gradient(
    135deg,
    var(--primary) 0%,
    var(--primary-dark) 100%
  );
  color: white;
}

.admin-btn-primary:hover {
  background: linear-gradient(
    135deg,
    var(--primary-light) 0%,
    var(--primary) 100%
  );
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 155, 199, 0.3);
}

.admin-btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--foreground);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.admin-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.admin-btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.admin-btn-danger:hover {
  background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* Admin Badge */
.admin-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.admin-badge-success {
  background: rgba(34, 197, 94, 0.2);
  color: #4ade80;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.admin-badge-warning {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.admin-badge-error {
  background: rgba(239, 68, 68, 0.2);
  color: #f87171;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.admin-badge-info {
  background: rgba(0, 155, 199, 0.2);
  color: var(--primary-light);
  border: 1px solid rgba(0, 155, 199, 0.3);
}
