{"name": "cyrus-blog-frontend", "private": true, "version": "1.0.0", "type": "module", "description": "Frontend for <PERSON>'s personal blog - built with React, TypeScript, and Vite", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview --host", "type-check": "tsc --noEmit", "clean": "rm -rf dist node_modules/.vite"}, "dependencies": {"@monaco-editor/react": "^4.7.0", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/prismjs": "^1.26.5", "@types/react-router-dom": "^5.3.3", "@types/react-syntax-highlighter": "^15.5.13", "@uiw/react-markdown-preview": "^5.1.4", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "framer-motion": "^12.23.9", "postcss": "^8.5.6", "prismjs": "^1.30.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-markdown": "^10.1.0", "react-router-dom": "^7.7.0", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "tailwindcss": "^4.1.11", "vite-plugin-monaco-editor": "^1.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^5.4.0"}}